'use client';

import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import ChatInterface from '@/components/chat/ChatInterface';
import SettingsModal from '@/components/settings/SettingsModal';

export default function Home() {
  const [activeConversationId, setActiveConversationId] = useState<string | undefined>();
  const [settingsVisible, setSettingsVisible] = useState(false);

  const handleSettingsClick = () => {
    setSettingsVisible(true);
  };

  const handleSettingsClose = () => {
    setSettingsVisible(false);
  };

  // Listen for new conversation creation
  useEffect(() => {
    const handleConversationCreated = (event: CustomEvent) => {
      const { conversationId } = event.detail;
      setActiveConversationId(conversationId);
    };

    window.addEventListener('conversationCreated', handleConversationCreated as EventListener);

    return () => {
      window.removeEventListener('conversationCreated', handleConversationCreated as EventListener);
    };
  }, []);

  return (
    <MainLayout
      onSettingsClick={handleSettingsClick}
      activeConversationId={activeConversationId}
      onConversationChange={setActiveConversationId}
    >
      <ChatInterface conversationId={activeConversationId} />

      <SettingsModal
        open={settingsVisible}
        onClose={handleSettingsClose}
      />
    </MainLayout>
  );
}
