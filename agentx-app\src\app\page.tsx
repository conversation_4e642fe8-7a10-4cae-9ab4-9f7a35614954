'use client';

import React, { useState } from 'react';
import { Modal } from 'antd';
import MainLayout from '@/components/layout/MainLayout';
import ChatInterface from '@/components/chat/ChatInterface';

export default function Home() {
  const [activeConversationId, setActiveConversationId] = useState<string | undefined>();
  const [settingsVisible, setSettingsVisible] = useState(false);

  const handleSettingsClick = () => {
    setSettingsVisible(true);
  };

  const handleSettingsClose = () => {
    setSettingsVisible(false);
  };

  return (
    <MainLayout
      onSettingsClick={handleSettingsClick}
      activeConversationId={activeConversationId}
      onConversationChange={setActiveConversationId}
    >
      <ChatInterface conversationId={activeConversationId} />

      <Modal
        title="Settings"
        open={settingsVisible}
        onCancel={handleSettingsClose}
        footer={null}
        width={600}
      >
        <div style={{ padding: '20px 0' }}>
          <p>Settings panel will be implemented in the next task.</p>
        </div>
      </Modal>
    </MainLayout>
  );
}
