'use client';

import React from 'react';
import { Layout, Typography, Button, Space } from 'antd';
import { SettingOutlined, MenuOutlined } from '@ant-design/icons';

const { Header: AntHeader } = Layout;
const { Title } = Typography;

interface HeaderProps {
  onSettingsClick: () => void;
  onMenuToggle: () => void;
  isMobile?: boolean;
}

export default function Header({ onSettingsClick, onMenuToggle, isMobile = false }: HeaderProps) {
  return (
    <AntHeader
      style={{
        background: '#fff',
        padding: '0 24px',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: 64,
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
        {isMobile && (
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={onMenuToggle}
            style={{ padding: '4px 8px' }}
          />
        )}
        <Title level={3} style={{ margin: 0, color: '#1677ff' }}>
          AgentX
        </Title>
      </div>

      <Space>
        <Button
          type="text"
          icon={<SettingOutlined />}
          onClick={onSettingsClick}
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 40,
            height: 40,
          }}
          title="Settings"
        />
      </Space>
    </AntHeader>
  );
}
