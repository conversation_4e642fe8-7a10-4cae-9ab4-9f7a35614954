'use client';

import React from 'react';
import { Layout, Button, List, Typography, Space, Popconfirm } from 'antd';
import { PlusOutlined, DeleteOutlined, MessageOutlined } from '@ant-design/icons';
import { Conversation } from '@/utils/storage';

const { Sider } = Layout;
const { Text } = Typography;

interface SidebarProps {
  conversations: Conversation[];
  activeConversationId?: string;
  onNewConversation: () => void;
  onSelectConversation: (id: string) => void;
  onDeleteConversation: (id: string) => void;
  collapsed: boolean;
  onCollapse: (collapsed: boolean) => void;
  isMobile?: boolean;
}

export default function Sidebar({
  conversations,
  activeConversationId,
  onNewConversation,
  onSelectConversation,
  onDeleteConversation,
  collapsed,
  onCollapse,
  isMobile = false,
}: SidebarProps) {
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const truncateTitle = (title: string, maxLength: number = 30) => {
    return title.length > maxLength ? `${title.slice(0, maxLength)}...` : title;
  };

  return (
    <Sider
      width={280}
      collapsedWidth={isMobile ? 0 : 80}
      collapsed={collapsed}
      onCollapse={onCollapse}
      style={{
        background: '#fafafa',
        borderRight: '1px solid #f0f0f0',
        height: '100%',
        overflow: 'hidden',
      }}
      breakpoint="lg"
      collapsible={!isMobile}
      trigger={null}
    >
      <div
        style={{
          padding: collapsed ? '16px 8px' : '16px',
          borderBottom: '1px solid #f0f0f0',
        }}
      >
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={onNewConversation}
          block={!collapsed}
          style={{
            height: 40,
            display: 'flex',
            alignItems: 'center',
            justifyContent: collapsed ? 'center' : 'flex-start',
          }}
        >
          {!collapsed && 'New Chat'}
        </Button>
      </div>

      <div
        style={{
          flex: 1,
          overflow: 'auto',
          padding: collapsed ? '8px 4px' : '8px',
        }}
      >
        <List
          dataSource={conversations}
          renderItem={(conversation) => (
            <List.Item
              key={conversation.id}
              style={{
                padding: collapsed ? '8px 4px' : '8px 12px',
                margin: '4px 0',
                borderRadius: 8,
                cursor: 'pointer',
                background: activeConversationId === conversation.id ? '#e6f4ff' : 'transparent',
                border: activeConversationId === conversation.id ? '1px solid #91caff' : '1px solid transparent',
                transition: 'all 0.2s',
              }}
              onClick={() => onSelectConversation(conversation.id)}
              onMouseEnter={(e) => {
                if (activeConversationId !== conversation.id) {
                  e.currentTarget.style.background = '#f5f5f5';
                }
              }}
              onMouseLeave={(e) => {
                if (activeConversationId !== conversation.id) {
                  e.currentTarget.style.background = 'transparent';
                }
              }}
            >
              <div style={{ width: '100%', minWidth: 0 }}>
                {collapsed ? (
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      height: 32,
                    }}
                  >
                    <MessageOutlined style={{ fontSize: 16, color: '#666' }} />
                  </div>
                ) : (
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <div style={{ flex: 1, minWidth: 0, marginRight: 8 }}>
                      <Text
                        strong
                        style={{
                          fontSize: 14,
                          color: '#262626',
                          display: 'block',
                          marginBottom: 4,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {truncateTitle(conversation.title)}
                      </Text>
                      <Text
                        type="secondary"
                        style={{
                          fontSize: 12,
                          display: 'block',
                        }}
                      >
                        {formatDate(conversation.updatedAt)}
                      </Text>
                    </div>
                    <Popconfirm
                      title="Delete conversation"
                      description="Are you sure you want to delete this conversation?"
                      onConfirm={(e) => {
                        e?.stopPropagation();
                        onDeleteConversation(conversation.id);
                      }}
                      okText="Yes"
                      cancelText="No"
                      placement="topRight"
                    >
                      <Button
                        type="text"
                        size="small"
                        icon={<DeleteOutlined />}
                        style={{
                          opacity: 0.6,
                          padding: '2px 4px',
                          height: 24,
                          width: 24,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onClick={(e) => e.stopPropagation()}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.opacity = '1';
                          e.currentTarget.style.background = '#ff4d4f';
                          e.currentTarget.style.color = '#fff';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.opacity = '0.6';
                          e.currentTarget.style.background = 'transparent';
                          e.currentTarget.style.color = 'inherit';
                        }}
                      />
                    </Popconfirm>
                  </div>
                )}
              </div>
            </List.Item>
          )}
          locale={{ emptyText: collapsed ? '' : 'No conversations yet' }}
        />
      </div>
    </Sider>
  );
}
