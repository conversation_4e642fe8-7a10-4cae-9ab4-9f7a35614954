/**
 * OpenAI API client with configurable endpoints
 * Supports streaming responses and error handling
 */

import OpenAI from 'openai';
import { ApiConfigStorage } from './storage';

// Types for OpenAI integration
export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface StreamCallbacks {
  onUpdate: (content: string) => void;
  onSuccess: (content: string) => void;
  onError: (error: Error) => void;
}

export interface ChatCompletionOptions {
  messages: ChatMessage[];
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
}

/**
 * OpenAI API Client wrapper
 */
export class OpenAIClient {
  private client: OpenAI | null = null;
  private config: { apiKey: string; baseUrl: string; modelName: string } | null = null;

  /**
   * Initialize the client with stored configuration
   */
  async initialize(): Promise<void> {
    try {
      this.config = await ApiConfigStorage.load();
      
      if (!this.config.apiKey) {
        throw new Error('API key not configured');
      }

      this.client = new OpenAI({
        apiKey: this.config.apiKey,
        baseURL: this.config.baseUrl,
        dangerouslyAllowBrowser: true, // Note: Only for demo purposes
      });
    } catch (error) {
      console.error('Failed to initialize OpenAI client:', error);
      throw error;
    }
  }

  /**
   * Update client configuration
   */
  async updateConfig(apiKey: string, baseUrl: string, modelName: string): Promise<void> {
    await ApiConfigStorage.save({ apiKey, baseUrl, modelName });
    await this.initialize();
  }

  /**
   * Check if client is ready
   */
  isReady(): boolean {
    return this.client !== null && this.config !== null;
  }

  /**
   * Get current model name
   */
  getModelName(): string {
    return this.config?.modelName || 'gpt-4o-mini';
  }

  /**
   * Create a non-streaming chat completion
   */
  async createChatCompletion(options: ChatCompletionOptions): Promise<string> {
    if (!this.client || !this.config) {
      throw new Error('OpenAI client not initialized');
    }

    try {
      const response = await this.client.chat.completions.create({
        model: this.config.modelName,
        messages: options.messages,
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || 2048,
        stream: false,
      });

      return response.choices[0]?.message?.content || '';
    } catch (error) {
      console.error('Chat completion failed:', error);
      throw new Error('Failed to get AI response');
    }
  }

  /**
   * Create a streaming chat completion
   */
  async createStreamingChatCompletion(
    options: ChatCompletionOptions,
    callbacks: StreamCallbacks
  ): Promise<void> {
    if (!this.client || !this.config) {
      throw new Error('OpenAI client not initialized');
    }

    let content = '';

    try {
      const stream = await this.client.chat.completions.create({
        model: this.config.modelName,
        messages: options.messages,
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || 2048,
        stream: true,
      });

      for await (const chunk of stream) {
        const delta = chunk.choices[0]?.delta?.content || '';
        if (delta) {
          content += delta;
          callbacks.onUpdate(content);
        }
      }

      callbacks.onSuccess(content);
    } catch (error) {
      console.error('Streaming chat completion failed:', error);
      callbacks.onError(new Error('Failed to get AI response'));
    }
  }

  /**
   * Validate API configuration
   */
  async validateConfig(apiKey: string, baseUrl: string, modelName: string): Promise<boolean> {
    try {
      const testClient = new OpenAI({
        apiKey,
        baseURL: baseUrl,
        dangerouslyAllowBrowser: true,
      });

      // Test with a simple request
      await testClient.chat.completions.create({
        model: modelName,
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 5,
      });

      return true;
    } catch (error) {
      console.error('API validation failed:', error);
      return false;
    }
  }

  /**
   * Get available models (if supported by the endpoint)
   */
  async getAvailableModels(): Promise<string[]> {
    if (!this.client) {
      throw new Error('OpenAI client not initialized');
    }

    try {
      const response = await this.client.models.list();
      return response.data.map(model => model.id);
    } catch (error) {
      console.warn('Failed to fetch models, using default list:', error);
      // Return common model names as fallback
      return [
        'gpt-4o',
        'gpt-4o-mini',
        'gpt-4-turbo',
        'gpt-4',
        'gpt-3.5-turbo',
      ];
    }
  }
}

// Singleton instance
export const openaiClient = new OpenAIClient();
