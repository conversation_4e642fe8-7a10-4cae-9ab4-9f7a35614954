You are an expert technical product manager specializing in feature development and creating comprehensive product requirements documents (PRDs). Your task is to generate a detailed and well-structured PRD based on the following instructions:

<prd_instructions>
# AgentX Functional Requirements

AgentX is a web-based conversational AI application utilizing OpenAI APIs, styled and structured with Ant Design X to ensure a professional UI/UX experience.

## 1. Tech Stack
- Frontend: React + Next.js + Ant Design X
- Supports Markdown parsing and code highlighting

## 2. Main Features

### 2.1 UI Design
- All interfaces are built using the Ant Design X component library
- Sidebar displays a list of past conversations
- Header area includes a gear/settings icon

### 2.2 OpenAI API Integration
- Integrates with OpenAI API, default parameters:
  - API Key: `***************************************************`
  - API Base URL: `https://c-z0-api-01.hash070.com/v1`
  - Default model name: `gpt-4o-mini`
- Supports input/changing of OpenAI API Key and Base URL via settings interface
- Supports model selection, Temperature, Max Tokens, and default System Prompt configuration

### 2.3 Local Storage
- Stores OpenAI API Key locally (must be encrypted or obfuscated)
- Stores chat history locally
- Stores model and system settings locally

### 2.4 Markdown and Code Highlighting
- Chat content supports Markdown parsing
- Supports code block highlighting

## 3. Others
- All features must ensure a professional and consistent UI/UX experience
- Refer to [Ant Design X](https://github.com/ant-design/x) and [OpenAI Cookbook](https://github.com/openai/openai-cookbook) for implementation
</prd_instructions>

Follow these steps to create the PRD:

1. Begin with a brief overview explaining the project and the purpose of the document.

2. Use sentence case for all headings except for the title of the document, which should be in title case.

3. Organize your PRD into the following sections:
   a. Introduction
   b. Product Overview
   c. Goals and Objectives
   d. Target Audience
   e. Features and Requirements
   f. User Stories and Acceptance Criteria
   g. Technical Requirements / Stack
   h. Design and User Interface

4. For each section, provide detailed and relevant information based on the PRD instructions. Ensure that you:
   - Use clear and concise language
   - Provide specific details and metrics where required
   - Maintain consistency throughout the document
   - Address all points mentioned in each section

5. When creating user stories and acceptance criteria:
   - List ALL necessary user stories including primary, alternative, and edge-case scenarios
   - Assign a unique requirement ID (e.g., ST-101) to each user story for direct traceability
   - Include at least one user story specifically for secure access or authentication if the application requires user identification
   - Include at least one user story specifically for Database modelling if the application requires a database
   - Ensure no potential user interaction is omitted
   - Make sure each user story is testable

6. Format your PRD professionally:
   - Use consistent styles
   - Include numbered sections and subsections
   - Use bullet points and tables where appropriate to improve readability
   - Ensure proper spacing and alignment throughout the document

7. Review your PRD to ensure all aspects of the project are covered comprehensively and that there are no contradictions or ambiguities.

Present your final PRD within <PRD> tags. Begin with the title of the document in title case, followed by each section with its corresponding content. Use appropriate subheadings within each section as needed.

Remember to tailor the content to the specific project described in the PRD instructions, providing detailed and relevant information for each section based on the given context.
