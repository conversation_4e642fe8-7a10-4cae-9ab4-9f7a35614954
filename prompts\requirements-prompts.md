I would like to create concise functional requirements for the following application:

The app is called AgentX. A web-based conversational AI application using OpenAI’s APIs, styled and structured with Ant Design X for professional UI/UX consistency.

My Requirements:
- UI built using Ant Design X (https://github.com/ant-design/x). use context7
- Sidebar to view past conversations
- API Integration with the OpenAI APIs (https://github.com/openai/openai-cookbook). use context7
  - api key is "***************************************************"
  - api base url is "https://c-z0-api-01.hash070.com/v1"
  - default modle name is "gpt-4o-mini"
- Accessible via a gear/settings icon in the header:
  - OpenAI API Key input field
  - Model selection
  - Temperature, Max Tokens
  - Default system prompt
- Local Storage
  - OpenAI API key (encrypted or obfuscated)
  - Chat history
  - Model and system settings
- Tech Stack
  - Frontend: React + Next.js + Ant Design X
  - Markdown Parsing
  - Code Highlighting

Output as markdown code, and save to requirements.md
