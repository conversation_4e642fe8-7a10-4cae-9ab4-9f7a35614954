Create a comprehensive project tracking document named `tasks.md` for the AgentX conversational AI application project. This document should serve as a living progress tracker that can be updated throughout the development lifecycle. 

The tasks.md file should include:

1. **Project Overview Section**: Brief description of AgentX and reference to the PRD.md
2. **Development Phases**: Break down the project into logical phases (Setup, Core Features, UI/UX, Testing, etc.)
3. **Task Categories**: Organize tasks by feature areas (Authentication, Chat Interface, Settings, Storage, etc.)
4. **Task Format**: Each task should include:
   - Unique task ID
   - Task description
   - Priority level (High/Medium/Low)
   - Status (Not Started/In Progress/Completed/Blocked)
   - Estimated effort
   - Dependencies (if any)

5. **Progress Tracking**: Include completion percentages and milestone markers
6. **Notes Section**: Space for important decisions, blockers, or changes

Structure the document to be easily maintainable and provide clear visibility into project progress. Use markdown formatting for readability and include checkboxes for completed items. Base the initial task breakdown on the user stories and requirements defined in the existing PRD.md file.