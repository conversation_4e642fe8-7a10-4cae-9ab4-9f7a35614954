# AgentX 功能性需求

AgentX 是一个基于 Web 的对话式 AI 应用，采用 OpenAI API，界面风格与结构统一使用 Ant Design X，旨在提供专业的 UI/UX 体验。

## 1. 技术栈
- 前端：React + Next.js + Ant Design X
- 支持 Markdown 解析与代码高亮

## 2. 主要功能

### 2.1 UI 设计
- 使用 Ant Design X 组件库实现所有界面
- 侧边栏展示历史会话列表
- 顶部 Header 区域包含齿轮/设置图标

### 2.2 OpenAI API 集成
- 集成 OpenAI API，默认参数：
  - API Key: `***************************************************`
  - API Base URL: `https://c-z0-api-01.hash070.com/v1`
  - 默认模型名称：`gpt-4o-mini`
- 支持通过设置界面输入/更换 OpenAI API Key 和 Base URL
- 支持模型选择、Temperature、Max Tokens、默认 System Prompt 配置

### 2.3 本地存储
- 本地存储 OpenAI API Key（需加密或混淆）
- 本地存储聊天历史
- 本地存储模型与系统设置

### 2.4 Markdown 与代码高亮
- 聊天内容支持 Markdown 解析
- 支持代码块高亮显示

## 3. 其他
- 所有功能需保证专业、统一的 UI/UX 体验
- 参考 [Ant Design X](https://github.com/ant-design/x) 与 [OpenAI Cookbook](https://github.com/openai/openai-cookbook) 实现
