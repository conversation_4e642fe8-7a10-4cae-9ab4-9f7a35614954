# AgentX Functional Requirements

AgentX is a web-based conversational AI application utilizing OpenAI APIs, styled and structured with Ant Design X to ensure a professional UI/UX experience.

## 1. Tech Stack
- Frontend: React + Next.js + Ant Design X
- Supports Markdown parsing and code highlighting

## 2. Main Features

### 2.1 UI Design
- All interfaces are built using the Ant Design X component library
- Sidebar displays a list of past conversations
- Header area includes a gear/settings icon

### 2.2 OpenAI API Integration
- Integrates with OpenAI API, default parameters:
  - API Key: `***************************************************`
  - API Base URL: `https://c-z0-api-01.hash070.com/v1`
  - Default model name: `gpt-4o-mini`
- Supports input/changing of OpenAI API Key and Base URL via settings interface
- Supports model selection, Temperature, Max Tokens, and default System Prompt configuration

### 2.3 Local Storage
- Stores OpenAI API Key locally (must be encrypted or obfuscated)
- Stores chat history locally
- Stores model and system settings locally

### 2.4 Markdown and Code Highlighting
- Chat content supports Markdown parsing
- Supports code block highlighting

## 3. Others
- All features must ensure a professional and consistent UI/UX experience
- Refer to [Ant Design X](https://github.com/ant-design/x) and [OpenAI Cookbook](https://github.com/openai/openai-cookbook) for implementation